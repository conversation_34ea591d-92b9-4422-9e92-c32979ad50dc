'use client';

import Script from 'next/script';
import { seoConfig } from '@/lib/metadata-utils';

export default function MicrosoftClarity() {
  const CLARITY_ID = seoConfig.analytics.microsoftClarity;

  if (!CLARITY_ID || CLARITY_ID === 'XXXXXXXXX') {
    return null; // Don't render if no valid Clarity ID is provided
  }

  return (
    <Script id="microsoft-clarity" strategy="afterInteractive">
      {`
        (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "${CLARITY_ID}");
      `}
    </Script>
  );
}

// Custom Clarity tracking functions
export const clarityIdentify = (userId: string, sessionData?: Record<string, string>) => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('identify', userId, sessionData);
  }
};

export const claritySet = (key: string, value: string) => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('set', key, value);
  }
};

export const clarityEvent = (eventName: string) => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity('event', eventName);
  }
};

// Extend Window interface for TypeScript
declare global {
  interface Window {
    clarity: (...args: unknown[]) => void;
  }
}
