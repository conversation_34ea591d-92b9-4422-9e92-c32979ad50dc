"use client";

import { useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

interface RefreshResponse {
  success: boolean;
  user: {
    id: string;
    email: string;
    name?: string;
    image?: string;
    role: string;
    emailVerified: boolean;
    isApproved: boolean;
  };
}

/**
 * Internal component that handles session refresh after email verification
 * This component uses useSearchParams and must be wrapped in Suspense
 */
function SessionRefreshHandlerContent() {
  const searchParams = useSearchParams();
  const { data: session, update } = useSession();

  useEffect(() => {
    const handleSessionRefresh = async () => {
      const emailVerified = searchParams.get('emailVerified');
      const refreshSession = searchParams.get('refreshSession');
      const userId = searchParams.get('userId');

      // Check if we need to refresh the session after email verification
      if (emailVerified === 'true' && refreshSession === 'true' && userId) {
        try {
          console.log("🔄 Email verified, refreshing session...");

          // First, fetch latest user data from our refresh endpoint
          const refreshResponse = await fetch('/api/auth/refresh-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
          });

          if (refreshResponse.ok) {
            const refreshData: RefreshResponse = await refreshResponse.json();
            console.log("✅ Latest user data:", refreshData.user);

            // Force NextAuth session update with latest data
            if (update) {
              await update(refreshData.user);
            }

            // Show success message
            toast.success("Email verified successfully! Welcome to your dashboard.");
          } else {
            console.error("❌ Failed to refresh session data");
            // Fallback to basic session update
            if (update) {
              await update();
            }
            toast.success("Email verified successfully!");
          }

          // Clean up URL parameters
          const url = new URL(window.location.href);
          url.searchParams.delete('emailVerified');
          url.searchParams.delete('refreshSession');
          url.searchParams.delete('userId');

          // Replace URL without page reload
          window.history.replaceState({}, '', url.toString());

        } catch (error) {
          console.error("❌ Error refreshing session:", error);
          toast.error("Session refresh failed. Please try logging in again.");
        }
      }
    };

    // Only run if we have search params and a session
    if (searchParams.toString() && session) {
      handleSessionRefresh();
    }
  }, [searchParams, session, update]);

  // This component doesn't render anything
  return null;
}

/**
 * Hook for manual session refresh
 * Use this when you need to refresh the session programmatically
 */
export function useSessionRefresh() {
  const { update } = useSession();

  const refreshSession = async () => {
    try {
      console.log("🔄 Manually refreshing session...");

      // Fetch latest user data from database
      const refreshResponse = await fetch('/api/auth/refresh-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (refreshResponse.ok) {
        const refreshData: RefreshResponse = await refreshResponse.json();
        console.log("✅ Session refreshed with latest data:", refreshData.user);

        // Update session with fresh database data
        if (update) {
          await update(refreshData.user);
        }
        return true;
      } else {
        console.error("❌ Failed to fetch latest user data");
        // Fallback to basic session update
        if (update) {
          await update();
        }
        return false;
      }
    } catch (error) {
      console.error("❌ Error refreshing session:", error);
      return false;
    }
  };

  return { refreshSession };
}

/**
 * Main component that handles session refresh after email verification
 * Place this in your dashboard layouts to automatically refresh sessions
 * Wrapped in Suspense to handle useSearchParams() requirement
 */
export function SessionRefreshHandler() {
  return (
    <Suspense fallback={null}>
      <SessionRefreshHandlerContent />
    </Suspense>
  );
}
