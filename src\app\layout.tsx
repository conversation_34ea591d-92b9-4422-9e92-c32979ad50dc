import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { ThemeProvider } from "@/components/theme-provider";
import { SonnerToaster } from "@/components/ui/sooner";
import { AuthProvider } from "@/components/providers/auth-provider";
import { generateRootMetadata } from "@/lib/metadata-utils";

//vercel packages analytics and speed
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

// Enhanced Analytics
import EnhancedAnalytics from "@/components/analytics/Analytics";
import { GoogleTagManagerNoScript } from "@/components/analytics/GoogleTagManager";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export async function generateMetadata(): Promise<Metadata> {
  return await generateRootMetadata();
}

import BodyIdFixer from "@/components/BodyIdFixer";
import TawkChat from "@/components/common/tawk-chat";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {


  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <GoogleTagManagerNoScript />
        <BodyIdFixer />
        <NuqsAdapter>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
            storageKey="theme-preferences"
          >
            <AuthProvider>
              {children}
              <Analytics />
              <SpeedInsights />
              <EnhancedAnalytics />
              <SonnerToaster />
              <TawkChat />
            </AuthProvider>
          </ThemeProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
}
