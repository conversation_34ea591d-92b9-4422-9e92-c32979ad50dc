'use client';

import { useEffect } from 'react';
import { useBlogAnalytics } from '@/hooks/use-enhanced-analytics';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  author: {
    id: string;
    name: string;
  };
  keywords: string[];
  pageViews: number;
}

interface BlogAnalyticsTrackerProps {
  post: BlogPost;
}

export default function BlogAnalyticsTracker({ post }: BlogAnalyticsTrackerProps) {
  const { trackBlogView, trackBlogEngagement } = useBlogAnalytics();

  useEffect(() => {
    // Track blog view on component mount
    trackBlogView({
      blog_id: post.id,
      blog_title: post.title,
      blog_slug: post.slug,
      blog_category: post.category.name,
      blog_category_slug: post.category.slug,
      author_id: post.author.id,
      author_name: post.author.name,
      keywords: post.keywords.join(', '),
      page_views: post.pageViews
    });

    // Track scroll depth
    let maxScrollDepth = 0;
    const trackScrollDepth = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);
      
      if (scrollPercent > maxScrollDepth) {
        maxScrollDepth = scrollPercent;
        
        // Track milestone scroll depths
        if (scrollPercent >= 25 && maxScrollDepth < 25) {
          trackBlogEngagement('scroll_25', { blog_slug: post.slug });
        } else if (scrollPercent >= 50 && maxScrollDepth < 50) {
          trackBlogEngagement('scroll_50', { blog_slug: post.slug });
        } else if (scrollPercent >= 75 && maxScrollDepth < 75) {
          trackBlogEngagement('scroll_75', { blog_slug: post.slug });
        } else if (scrollPercent >= 90 && maxScrollDepth < 90) {
          trackBlogEngagement('scroll_complete', { blog_slug: post.slug });
        }
      }
    };

    // Track time on page
    const startTime = Date.now();
    const trackTimeOnPage = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      
      // Track time milestones
      if (timeSpent >= 30) {
        trackBlogEngagement('time_30s', { 
          blog_slug: post.slug, 
          time_spent: timeSpent 
        });
      }
      if (timeSpent >= 60) {
        trackBlogEngagement('time_1min', { 
          blog_slug: post.slug, 
          time_spent: timeSpent 
        });
      }
      if (timeSpent >= 180) {
        trackBlogEngagement('time_3min', { 
          blog_slug: post.slug, 
          time_spent: timeSpent 
        });
      }
    };

    // Add event listeners
    window.addEventListener('scroll', trackScrollDepth);
    
    // Track time milestones
    const timeInterval = setInterval(trackTimeOnPage, 30000); // Check every 30 seconds

    // Track when user leaves the page
    const handleBeforeUnload = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      trackBlogEngagement('page_exit', {
        blog_slug: post.slug,
        time_spent: timeSpent,
        max_scroll_depth: maxScrollDepth
      });
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', trackScrollDepth);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      clearInterval(timeInterval);
    };
  }, [post, trackBlogView, trackBlogEngagement]);

  // Track CTA clicks
  useEffect(() => {
    const trackCTAClicks = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.closest('[data-cta="blog-insert"]')) {
        trackBlogEngagement('cta_click', {
          blog_slug: post.slug,
          cta_type: 'blog_insert'
        });
      }
    };

    document.addEventListener('click', trackCTAClicks);
    
    return () => {
      document.removeEventListener('click', trackCTAClicks);
    };
  }, [post.slug, trackBlogEngagement]);

  return null; // This component doesn't render anything
}
