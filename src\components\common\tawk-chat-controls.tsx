'use client';

import { useTawkAction } from 'tawk-react';

const TawkChatControls = () => {
  const { maximize, minimize, toggle, endChat, showWidget, hideWidget } = useTawkAction();

  return (
    <div className="fixed bottom-20 right-4 bg-white p-4 rounded-lg shadow-lg border z-40">
      <h4 className="text-sm font-semibold mb-3 text-gray-800">Chat Controls</h4>
      <div className="flex flex-col gap-2">
        <button
          onClick={maximize}
          className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Maximize
        </button>
        <button
          onClick={minimize}
          className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Minimize
        </button>
        <button
          onClick={toggle}
          className="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Toggle
        </button>
        <button
          onClick={showWidget}
          className="px-3 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
        >
          Show Widget
        </button>
        <button
          onClick={hideWidget}
          className="px-3 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
        >
          Hide Widget
        </button>
        <button
          onClick={endChat}
          className="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          End Chat
        </button>
      </div>
    </div>
  );
};

export default TawkChatControls;
