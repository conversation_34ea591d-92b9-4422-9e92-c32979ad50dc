'use client';

import Script from 'next/script';
import { seoConfig } from '@/lib/metadata-utils';

export default function GoogleTagManager() {
  const GTM_ID = seoConfig.analytics.googleTagManager;

  if (!GTM_ID || GTM_ID === 'GTM-XXXXXXX') {
    return null; // Don't render if no valid GTM ID is provided
  }

  return (
    <>
      {/* Google Tag Manager */}
      <Script id="google-tag-manager" strategy="afterInteractive">
        {`
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${GTM_ID}');
        `}
      </Script>
    </>
  );
}

// GTM NoScript component for body
export function GoogleTagManagerNoScript() {
  const GTM_ID = seoConfig.analytics.googleTagManager;

  if (!GTM_ID || GTM_ID === 'GTM-XXXXXXX') {
    return null;
  }

  return (
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
        height="0"
        width="0"
        style={{ display: 'none', visibility: 'hidden' }}
      />
    </noscript>
  );
}

// Custom GTM event tracking
export const pushToDataLayer = (event: string, data?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push({
      event,
      ...data
    });
  }
};

// Academic writing specific GTM events
export const trackGTMOrderEvent = (eventType: string, orderData: Record<string, unknown>) => {
  pushToDataLayer('order_event', {
    event_type: eventType,
    ...orderData
  });
};

export const trackGTMUserEvent = (eventType: string, userData: Record<string, unknown>) => {
  pushToDataLayer('user_event', {
    event_type: eventType,
    ...userData
  });
};

export const trackGTMBlogEvent = (eventType: string, blogData: Record<string, unknown>) => {
  pushToDataLayer('blog_event', {
    event_type: eventType,
    ...blogData
  });
};

// Extend Window interface for TypeScript
declare global {
  interface Window {
    dataLayer: Record<string, unknown>[];
  }
}
