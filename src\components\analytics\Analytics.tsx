'use client';

import { Suspense } from 'react';
import GoogleAnalytics from './GoogleAnalytics';
import MicrosoftClarity from './MicrosoftClarity';
import GoogleTagManager from './GoogleTagManager';

export default function Analytics() {
  return (
    <Suspense fallback={null}>
      <GoogleTagManager />
      <GoogleAnalytics />
      <MicrosoftClarity />
    </Suspense>
  );
}

// Export individual components for selective use
export { GoogleAnalytics, MicrosoftClarity, GoogleTagManager };

// Export tracking functions
export {
  trackEvent,
  trackPageView,
  trackOrderEvent,
  trackBlogEvent,
  trackUserEvent
} from './GoogleAnalytics';

export {
  clarityIdentify,
  claritySet,
  clarityEvent
} from './MicrosoftClarity';

export {
  pushToDataLayer,
  trackGTMOrderEvent,
  trackGTMUserEvent,
  trackGTMBlogEvent
} from './GoogleTagManager';
