import { Metadata } from "next";
import prisma from "@/lib/prisma";

// SEO Configuration
export const seoConfig = {
  // Site Information
  siteName: process.env.NEXT_PUBLIC_COMPANY_NAME || 'Essay Scholar',
  siteUrl: process.env.NEXTAUTH_URL || 'https://essayscholars.us',
  siteDescription: 'Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.',

  // Default Meta
  defaultTitle: `${process.env.NEXT_PUBLIC_COMPANY_NAME || 'Essay Scholar'} - Professional Academic Writing Services`,
  titleTemplate: `%s | ${process.env.NEXT_PUBLIC_COMPANY_NAME || 'Essay Scholar'}`,

  // Keywords
  keywords: [
    'academic writing services',
    'essay help',
    'research paper writing',
    'dissertation assistance',
    'homework help',
    'professional writers',
    'student support',
    'academic assistance',
    'college essay help',
    'university writing services',
    'custom essay writing',
    'term paper help',
    'literature review',
    'thesis writing',
    'academic papers',
    process.env.NEXT_PUBLIC_COMPANY_NAME || 'Essay Scholar'
  ],

  // Social Media
  social: {
    twitter: process.env.NEXT_PUBLIC_TWITTER_HANDLE || '@essayscholar',
    facebook: process.env.NEXT_PUBLIC_FACEBOOK_URL || 'https://facebook.com/essayscholar',
    linkedin: process.env.NEXT_PUBLIC_LINKEDIN_URL || 'https://linkedin.com/company/essayscholar',
    instagram: process.env.NEXT_PUBLIC_INSTAGRAM_URL || 'https://instagram.com/essayscholar',
  },

  // Contact Information
  contact: {
    email: process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>',
    phone: {
      usa: process.env.NEXT_PUBLIC_PHONE_USA || '******-123-4567',
      support: process.env.NEXT_PUBLIC_PHONE_SUPPORT || '******-123-4567'
    },
    address: {
      street: process.env.NEXT_PUBLIC_ADDRESS_STREET || '123 Academic Street',
      city: process.env.NEXT_PUBLIC_ADDRESS_CITY || 'Education City',
      state: process.env.NEXT_PUBLIC_ADDRESS_STATE || 'CA',
      country: process.env.NEXT_PUBLIC_ADDRESS_COUNTRY || 'US',
      zipCode: process.env.NEXT_PUBLIC_ADDRESS_ZIP || '12345'
    }
  },

  // Business Information
  business: {
    name: process.env.NEXT_PUBLIC_COMPANY_NAME || 'Essay Scholar',
    foundingDate: process.env.NEXT_PUBLIC_FOUNDING_YEAR || '2020',
    priceRange: '$$',
    rating: {
      value: '4.9',
      count: '2500'
    },
    services: [
      'Essay Writing',
      'Research Papers',
      'Dissertation Writing',
      'Term Papers',
      'Literature Reviews',
      'Thesis Writing',
      'Case Studies',
      'Academic Editing',
      'Proofreading Services'
    ]
  },

  // Images
  images: {
    logo: '/opengraph-image.png',
    ogImage: '/opengraph-image.png',
    favicon: '/favicon.ico'
  },

  // Analytics & Verification
  analytics: {
    googleAnalytics: process.env.NEXT_PUBLIC_GA_ID || 'G-XXXXXXXXXX',
    googleTagManager: process.env.NEXT_PUBLIC_GTM_ID || 'GTM-XXXXXXX',
    microsoftClarity: process.env.NEXT_PUBLIC_CLARITY_ID || 'XXXXXXXXX',
  },

  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION || 'your-google-verification-code',
    bing: process.env.NEXT_PUBLIC_BING_VERIFICATION || 'your-bing-verification-code',
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION || 'your-yandex-verification-code',
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION || 'your-yahoo-verification-code'
  }
};

// Get company info for metadata generation
export async function getCompanyInfo() {
  try {
    const companyInfo = await prisma.companyInfo.findFirst();
    
    if (!companyInfo) {
      // Return default values if no company info exists
      return {
        companyName: "Essay App",
        description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
        website: "https://essayscholars.us",
        supportEmail: "<EMAIL>",
      };
    }

    return {
      companyName: companyInfo.companyName,
      description: companyInfo.description || "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
      website: companyInfo.website || "https://essayscholars.us",
      supportEmail: companyInfo.supportEmail,
    };
  } catch (error) {
    console.error("Error fetching company info for metadata:", error);
    // Return default values on error
    return {
      companyName: "Essay App",
      description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
      website: "https://essayscholars.us",
      supportEmail: "<EMAIL>",
    };
  }
}

// Generate dynamic metadata with company info
export async function generateDynamicMetadata({
  title,
  description,
  keywords = [],
  path = "",
  ogImage,
}: {
  title: string;
  description: string;
  keywords?: string[];
  path?: string;
  ogImage?: string;
}): Promise<Metadata> {
  const companyInfo = await getCompanyInfo();
  const baseUrl = seoConfig.siteUrl;
  const fullUrl = `${baseUrl}${path}`;
  const defaultOgImage = `${baseUrl}/opengraph-image.png`;

  return {
    title: `${title} | ${companyInfo.companyName}`,
    description,
    keywords: [...keywords, ...seoConfig.keywords],
    authors: [{ name: companyInfo.companyName }],
    creator: companyInfo.companyName,
    publisher: companyInfo.companyName,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    openGraph: {
      title: `${title} | ${companyInfo.companyName}`,
      description,
      url: fullUrl,
      siteName: companyInfo.companyName,
      images: [
        {
          url: ogImage || defaultOgImage,
          width: 1200,
          height: 630,
          alt: `${title} - ${companyInfo.companyName}`,
        },
      ],
      locale: "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | ${companyInfo.companyName}`,
      description,
      creator: seoConfig.social.twitter,
      images: [ogImage || defaultOgImage],
    },
    alternates: {
      canonical: fullUrl,
    },
    verification: {
      google: seoConfig.verification.google,
      other: {
        'msvalidate.01': seoConfig.verification.bing,
        'yandex-verification': seoConfig.verification.yandex,
        'yahoo-site-verification': seoConfig.verification.yahoo,
      },
    },
  };
}

// Generate root layout metadata
export async function generateRootMetadata(): Promise<Metadata> {
  const companyInfo = await getCompanyInfo();
  const baseUrl = seoConfig.siteUrl;

  return {
    title: {
      default: companyInfo.companyName,
      template: seoConfig.titleTemplate,
    },
    description: companyInfo.description,
    keywords: seoConfig.keywords,
    authors: [{ name: companyInfo.companyName }],
    creator: companyInfo.companyName,
    publisher: companyInfo.companyName,
    metadataBase: new URL(baseUrl),
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    openGraph: {
      title: companyInfo.companyName,
      description: companyInfo.description,
      url: baseUrl,
      siteName: companyInfo.companyName,
      images: [
        {
          url: `${baseUrl}/opengraph-image.png`,
          width: 1200,
          height: 630,
          alt: `${companyInfo.companyName} - Academic Writing Services`,
        },
      ],
      locale: "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: companyInfo.companyName,
      description: companyInfo.description,
      creator: seoConfig.social.twitter,
      images: [`${baseUrl}/opengraph-image.png`],
    },
    verification: {
      google: seoConfig.verification.google,
      other: {
        'msvalidate.01': seoConfig.verification.bing,
        'yandex-verification': seoConfig.verification.yandex,
        'yahoo-site-verification': seoConfig.verification.yahoo,
      },
    },
    icons: {
      icon: "/favicon.ico",
      shortcut: "/favicon.ico",
      apple: "/apple-touch-icon.png",
    },
    manifest: "/site.webmanifest",
  };
}

// Generate structured data for different page types
type ServiceData = {
  name?: string;
  description?: string;
  serviceType?: string[] | string;
};

export const generateStructuredData = (
  type: 'organization' | 'localBusiness' | 'service' | 'educationalOrganization',
  data?: ServiceData
) => {
  const baseUrl = seoConfig.siteUrl;

  switch (type) {
    case 'organization':
      return {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": seoConfig.business.name,
        "url": baseUrl,
        "logo": `${baseUrl}${seoConfig.images.logo}`,
        "description": seoConfig.siteDescription,
        "foundingDate": seoConfig.business.foundingDate,
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": seoConfig.contact.phone.usa,
          "contactType": "customer service",
          "areaServed": "US",
          "availableLanguage": "English",
          "hoursAvailable": {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": [
              "Monday", "Tuesday", "Wednesday", "Thursday",
              "Friday", "Saturday", "Sunday"
            ],
            "opens": "00:00",
            "closes": "23:59"
          }
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": seoConfig.contact.address.street,
          "addressLocality": seoConfig.contact.address.city,
          "addressRegion": seoConfig.contact.address.state,
          "postalCode": seoConfig.contact.address.zipCode,
          "addressCountry": seoConfig.contact.address.country
        },
        "sameAs": [
          seoConfig.social.facebook,
          seoConfig.social.linkedin,
          seoConfig.social.instagram,
          seoConfig.social.twitter.replace('@', 'https://twitter.com/')
        ],
        "serviceType": seoConfig.business.services,
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": seoConfig.business.rating.value,
          "reviewCount": seoConfig.business.rating.count
        }
      };

    case 'educationalOrganization':
      return {
        "@context": "https://schema.org",
        "@type": "EducationalOrganization",
        "name": seoConfig.business.name,
        "url": baseUrl,
        "logo": `${baseUrl}${seoConfig.images.logo}`,
        "description": seoConfig.siteDescription,
        "serviceType": "Academic Writing Services",
        "educationalCredentialAwarded": "Academic Support",
        "hasCredential": "Professional Writing Certification",
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": seoConfig.contact.phone.usa,
          "contactType": "customer service",
          "email": seoConfig.contact.email
        }
      };

    case 'service':
      return {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": data?.name || "Academic Writing Services",
        "description": data?.description || seoConfig.siteDescription,
        "provider": {
          "@type": "Organization",
          "name": seoConfig.business.name,
          "url": baseUrl
        },
        "areaServed": ["US", "Global"],
        "serviceType": data?.serviceType || seoConfig.business.services,
        "offers": {
          "@type": "Offer",
          "availability": "https://schema.org/InStock",
          "priceRange": seoConfig.business.priceRange
        }
      };

    default:
      return null;
  }
};
