'use client';

import Script from 'next/script';
import { seoConfig } from '@/lib/metadata-utils';

export default function GoogleAnalytics() {
  const GA_MEASUREMENT_ID = seoConfig.analytics.googleAnalytics;

  if (!GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {
    return null; // Don't render if no valid GA ID is provided
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_MEASUREMENT_ID}', {
            page_title: document.title,
            page_location: window.location.href,
            send_page_view: true
          });
        `}
      </Script>
    </>
  );
}

// Custom event tracking functions
export const trackEvent = (eventName: string, parameters?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

export const trackPageView = (url: string, title?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', seoConfig.analytics.googleAnalytics, {
      page_title: title || document.title,
      page_location: url,
    });
  }
};

// Track specific academic writing events
export const trackOrderEvent = (eventType: 'order_started' | 'order_completed' | 'order_cancelled', orderData?: Record<string, unknown>) => {
  trackEvent(eventType, {
    event_category: 'order',
    ...orderData
  });
};

export const trackBlogEvent = (eventType: 'blog_view' | 'blog_share' | 'blog_engagement', blogData?: Record<string, unknown>) => {
  trackEvent(eventType, {
    event_category: 'blog',
    ...blogData
  });
};

export const trackUserEvent = (eventType: 'user_registration' | 'user_login' | 'user_logout', userData?: Record<string, unknown>) => {
  trackEvent(eventType, {
    event_category: 'user',
    ...userData
  });
};

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
  }
}
