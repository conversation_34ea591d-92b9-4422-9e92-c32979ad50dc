# Social Login JWT Token Refresh Fix

## Problem Description

Users who verified their email after social login were still being redirected to the "verify-email-required" page instead of directly accessing their dashboard. This occurred because JWT tokens are stateless and don't automatically reflect database changes like `emailVerified` status updates.

## Root Cause

The JWT callback in `src/auth.ts` had conditional logic that prevented database refresh for social logins:

```javascript
// OLD PROBLEMATIC CODE
if (token.id && typeof window === "undefined" && !user && !account) {
  // Database refresh only happened when NO account was present
  // This excluded social logins which always have an account object
}
```

## Solution Overview

We implemented a **modern, comprehensive JWT refresh system** that ensures tokens always reflect the latest database state while maintaining security and performance.

## Changes Made

### 1. Enhanced JWT Callback (`src/auth.ts`)

**Key Changes:**
- Removed restrictive conditions that prevented database refresh for social logins
- Added comprehensive logging for debugging
- Implemented shorter JWT refresh intervals (15 minutes vs 30 days)
- Always fetch latest user data from database for existing users

**New Logic:**
```javascript
// MODERN FIX: Always refresh user data from database for existing users
if (token.id && typeof window === "undefined") {
  // Fetch latest user data regardless of login method
  const latestUser = await prisma.user.findUnique({
    where: { id: token.id },
    select: { role: true, email: true, emailVerified: true, isApproved: true },
  });
  
  if (latestUser) {
    token.role = latestUser.role;
    token.emailVerified = latestUser.emailVerified;
    token.isApproved = latestUser.isApproved;
  }
}
```

### 2. Shorter JWT Refresh Intervals

**Added to auth config:**
```javascript
session: { 
  strategy: "jwt",
  maxAge: 15 * 60, // 15 minutes (was 30 days)
},
jwt: {
  maxAge: 15 * 60, // 15 minutes
},
```

### 3. Session Refresh Infrastructure

**Created new files:**
- `src/lib/session-utils.ts` - Utility functions for manual session refresh
- `src/components/SessionRefreshHandler.tsx` - Client-side component for automatic refresh
- `src/app/api/users/[userId]/verification-status/route.ts` - API to check verification status

**Enhanced existing:**
- `src/app/api/auth/refresh-session/route.ts` - Added `isApproved` field support

### 4. Email Verification Integration

**Updated `src/app/api/auth/verify-email/route.ts`:**
```javascript
// Add query parameters to trigger session refresh
const redirectUrl = new URL(redirectPath, baseUrl);
redirectUrl.searchParams.set('emailVerified', 'true');
redirectUrl.searchParams.set('refreshSession', 'true');
redirectUrl.searchParams.set('userId', updatedUser.id);
```

### 5. Dashboard Layout Integration

**Added SessionRefreshHandler to all dashboard layouts:**
- `src/app/(without-footer)/client/dashboard/layout.tsx`
- `src/app/(without-footer)/writer/layout.tsx`
- `src/app/(without-footer)/admin/layout.tsx`

## How It Works Now

### Social Login Flow (Fixed)

1. **User clicks social login** → OAuth provider authentication
2. **OAuth callback received** → JWT callback triggered
3. **Database refresh happens** → Latest `emailVerified` status fetched
4. **Token updated** → Reflects current database state
5. **Redirect logic** → Uses updated token data for routing decisions

### Email Verification Flow (Enhanced)

1. **User clicks verification link** → Email verified in database
2. **Redirect with refresh params** → `?emailVerified=true&refreshSession=true`
3. **SessionRefreshHandler detects params** → Triggers session refresh
4. **Fresh data fetched** → Latest user data from database
5. **Session updated** → NextAuth session reflects changes
6. **Direct dashboard access** → No more verification page redirect

### Automatic Refresh (New)

- **15-minute JWT expiry** → Forces regular database sync
- **Client-side refresh handler** → Detects verification success
- **Manual refresh hook** → Available for programmatic updates

## Benefits

1. **Immediate Reflection** - Database changes appear instantly in JWT tokens
2. **Modern Standards** - Follows current best practices for JWT refresh
3. **Security Maintained** - Shorter token lifespans improve security
4. **User Experience** - Seamless flow after email verification
5. **Debugging Support** - Comprehensive logging for troubleshooting

## Usage Examples

### Manual Session Refresh
```javascript
import { useSessionRefresh } from '@/components/SessionRefreshHandler';

const { refreshSession } = useSessionRefresh();

// After updating user data
await updateUserProfile();
await refreshSession(); // Sync session with database
```

### Programmatic Refresh
```javascript
import { updateSessionData } from '@/lib/session-utils';

// Force session update with specific data
await updateSessionData({ emailVerified: true });
```

## Testing Verification

1. **Create account via social login** (Google/Twitter/Facebook)
2. **Verify email** via verification link
3. **Login again** → Should go directly to dashboard
4. **No "verify-email-required" page** should appear

## Performance Considerations

- **15-minute JWT refresh** balances security and performance
- **Database queries** only on token refresh (not every request)
- **Client-side caching** prevents unnecessary API calls
- **Conditional refresh** only when needed

## Backward Compatibility

All existing functionality remains intact:
- Credential login works as before
- Role-based access control maintained
- Email verification process unchanged
- Error handling preserved

This fix resolves the JWT stateless token issue while implementing modern authentication patterns that scale with your application's growth.
