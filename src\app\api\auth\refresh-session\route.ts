import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";
import prisma from "@/lib/prisma";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authConfig);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Fetch the latest user data from database
    const latestUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        role: true,
        isApproved: true,
        name: true,
        image: true
      },
    });

    if (!latestUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    console.log("🔄 Session refresh requested for user:", {
      userId: latestUser.id,
      email: latestUser.email,
      emailVerified: latestUser.emailVerified,
      role: latestUser.role,
      isApproved: latestUser.isApproved
    });

    // Return the latest user data
    return NextResponse.json({
      success: true,
      user: {
        id: latestUser.id,
        email: latestUser.email,
        name: latestUser.name,
        image: latestUser.image,
        role: latestUser.role,
        emailVerified: latestUser.emailVerified,
        isApproved: latestUser.isApproved,
      }
    });

  } catch (error) {
    console.error("❌ Error refreshing session:", error);
    return NextResponse.json(
      { error: "Failed to refresh session" }, 
      { status: 500 }
    );
  }
}
