import AcademicWritingLandingPage from "@/components/landing-page";
import React from "react";
import { Metadata } from "next";
import { generateDynamicMetadata } from "@/lib/metadata-utils";
import { HomepageStructuredData } from "@/components/seo/StructuredData";

export async function generateMetadata(): Promise<Metadata> {
  return await generateDynamicMetadata({
    title: "Professional Academic Writing Services - Expert Help for Students",
    description: "Get expert help with essays, research papers, dissertations, and more. Professional academic writers, 24/7 support, plagiarism-free content, and on-time delivery guaranteed.",
    keywords: [
      "academic writing services",
      "essay help",
      "research paper writing",
      "dissertation assistance",
      "homework help",
      "professional writers",
      "student support",
      "academic assistance",
      "college essay help",
      "university writing services"
    ],
    path: "/",
    ogImage: "/opengraph-image.png"
  });
}

export default function Home() {
  return (
    <>
      <HomepageStructuredData />
      <div className="">
        <main className="min-h-screen">
          <AcademicWritingLandingPage />
        </main>
      </div>
    </>
  );
}


