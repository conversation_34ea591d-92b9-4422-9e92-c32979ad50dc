"use client";
import React, { useMemo, useRef, useEffect, useState, useCallback } from "react";
import { createPortal } from "react-dom";
import dynamic from "next/dynamic";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Quote,
  Code,
  Link,
  Image,
  Video,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Palette,
  Heading1,
  Heading2,
  X
} from "lucide-react";
import "quill/dist/quill.snow.css";
import "./headless-editor.css";

// Define proper types for ReactQuill
type SelectionChangeHandler = (range: { index: number; length: number } | null, source?: string) => void;

interface QuillEditor {
  getSelection: () => { index: number; length: number } | null;
  setSelection: (range: { index: number; length: number }) => void;
  focus: () => void;
  format: (name: string, value: unknown) => void;
  getFormat: (range?: { index: number; length: number }) => Record<string, unknown>;
  insertEmbed: (index: number, type: string, value: string) => void;
  getBounds: (index: number, length?: number) => { top: number; left: number; height: number; width: number };
  container: HTMLElement;
  on(event: 'selection-change', handler: SelectionChangeHandler): void;
  on(event: string, handler: (...args: unknown[]) => void): void;
  off(event: 'selection-change', handler: SelectionChangeHandler): void;
  off(event: string, handler: (...args: unknown[]) => void): void;
}

interface ReactQuillComponent {
  getEditor: () => QuillEditor;
}

// Dynamically import ReactQuill with proper typing
const ReactQuill = dynamic(() => import("react-quill-new"), {
  ssr: false,
  loading: () => (
    <div className="quill-editor">
      <div className="h-32 bg-gray-50 border border-gray-200 rounded animate-pulse flex items-center justify-center">
        <span className="text-gray-400">Loading editor...</span>
      </div>
    </div>
  ),
}) as React.ComponentType<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  className?: string;
  modules?: Record<string, unknown>;
  formats?: string[];
  theme?: string;
  ref?: React.RefObject<ReactQuillComponent | null>;
}>;

// Default color palette that Quill uses
const colors = [
  '#000000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff',
  '#ffffff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff',
  '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff',
  '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2',
  '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'
];

const toolbarOptions = [
  ["bold", "italic", "underline", "strike"],
  ["blockquote", "code-block"],
  ["link", "image", "video", "formula"],
  [{ header: 1 }, { header: 2 }],
  [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
  [{ script: "sub" }, { script: "super" }],
  [{ indent: "-1" }, { indent: "+1" }],
  [{ direction: "rtl" }],
  [{ size: ["small", false, "large", "huge"] }],
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ color: colors }, { background: colors }],
  [{ font: [] }],
  [{ align: [] }],
  ["clean"],
];

// Map toolbar button to tooltip label
const TOOLTIP_LABELS: Record<string, string> = {
  bold: "Bold",
  italic: "Italic",
  underline: "Underline",
  strike: "Strikethrough",
  blockquote: "Blockquote",
  "code-block": "Code Block",
  link: "Insert Link",
  image: "Insert Image",
  video: "Insert Video",
  formula: "Insert Formula",
  header: "Header",
  list: "List",
  ordered: "Numbered List",
  bullet: "Bullet List",
  check: "Checklist",
  script: "Script",
  sub: "Subscript",
  super: "Superscript",
  indent: "Indent",
  direction: "Text Direction (RTL)",
  size: "Font Size",
  color: "Font Color",
  background: "Background Color",
  font: "Font Family",
  align: "Text Align",
  clean: "Remove Formatting",
  "-1": "Decrease Indent",
  "+1": "Increase Indent",
  "rtl": "Right to Left"
};

// Items that should be rendered as select dropdowns
const SELECT_ITEMS = new Set(['size', 'header', 'color', 'background', 'font', 'align']);

// Helper function to render select options properly
function renderSelectOptions(item: Record<string, unknown>, key: string) {
  if (!Object.prototype.hasOwnProperty.call(item, key)) {
    return null;
  }

  const value = item[key];
  
  if (Array.isArray(value)) {
    return value.map((val, idx) => {
      if (val === false) {
        return (
          <option value="" key={idx}>
            Normal
          </option>
        );
      }
      return (
        <option value={val} key={idx}>
          {key === 'header' && typeof val === 'number' ? `Heading ${val}` : 
           key === 'size' && val === 'small' ? 'Small' :
           key === 'size' && val === 'large' ? 'Large' :
           key === 'size' && val === 'huge' ? 'Huge' :
           String(val)}
        </option>
      );
    });
  }

  return null;
}

// Custom Toolbar with shadcn tooltips
function QuillCustomToolbar() {
  return (
    <div id="quill-toolbar">
      <TooltipProvider>
        {toolbarOptions.map((group, i) => (
          <span key={i} className="ql-formats">
            {group.map((item, j) => {
              if (typeof item === "string") {
                // String items are always buttons
                const label = TOOLTIP_LABELS[item] || item;
                return (
                  <Tooltip key={j}>
                    <TooltipTrigger asChild>
                      <button type="button" className={`ql-${item}`} aria-label={label} />
                    </TooltipTrigger>
                    <TooltipContent>{label}</TooltipContent>
                  </Tooltip>
                );
              } else {
                // Handle object items
                const key = Object.keys(item)[0];
                const value = item[key as keyof typeof item];

                // Type guard to check if value is an array
                const isArrayValue = Array.isArray(value);

                // Check if this should be a select dropdown
                if (SELECT_ITEMS.has(key) && isArrayValue && (value as unknown[]).length > 0) {
                  const label = TOOLTIP_LABELS[key] || key;
                  return (
                    <Tooltip key={j}>
                      <TooltipTrigger asChild>
                        <select className={`ql-${key}`} aria-label={label}>
                          <option value="">
                            {key === 'header' ? 'Normal' :
                             key === 'size' ? 'Normal' :
                             key === 'font' ? 'Sans Serif' :
                             key === 'align' ? 'Left' :
                             'Default'}
                          </option>
                          {renderSelectOptions(item, key)}
                        </select>
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  );
                } else if (SELECT_ITEMS.has(key) && isArrayValue && (value as unknown[]).length === 0) {
                  // Handle empty arrays for color, background, font, align
                  const label = TOOLTIP_LABELS[key] || key;
                  return (
                    <Tooltip key={j}>
                      <TooltipTrigger asChild>
                        <select className={`ql-${key}`} aria-label={label}>
                          {/* These will be populated by Quill with theme defaults */}
                        </select>
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  );
                } else {
                  // Everything else should be buttons (list, script, indent, direction)
                  const label = TOOLTIP_LABELS[key] || TOOLTIP_LABELS[String(value)] || `${key}: ${value}`;
                  return (
                    <Tooltip key={j}>
                      <TooltipTrigger asChild>
                        <button
                          type="button"
                          className={`ql-${key}`}
                          value={String(value)}
                          aria-label={label}
                        />
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  );
                }
              }
            })}
          </span>
        ))}
      </TooltipProvider>
    </div>
  );
}

// Headless toolbar configuration
const headlessToolbarGroups = [
  {
    name: "formatting",
    items: [
      { type: "bold", icon: Bold, label: "Bold" },
      { type: "italic", icon: Italic, label: "Italic" },
      { type: "underline", icon: Underline, label: "Underline" },
      { type: "strike", icon: Strikethrough, label: "Strikethrough" },
    ]
  },
  {
    name: "blocks",
    items: [
      { type: "blockquote", icon: Quote, label: "Quote" },
      { type: "code-block", icon: Code, label: "Code Block" },
      { type: "header", value: 1, icon: Heading1, label: "Heading 1" },
      { type: "header", value: 2, icon: Heading2, label: "Heading 2" },
    ]
  },
  {
    name: "lists",
    items: [
      { type: "list", value: "ordered", icon: ListOrdered, label: "Numbered List" },
      { type: "list", value: "bullet", icon: List, label: "Bullet List" },
    ]
  },
  {
    name: "alignment",
    items: [
      { type: "align", value: "", icon: AlignLeft, label: "Align Left" },
      { type: "align", value: "center", icon: AlignCenter, label: "Align Center" },
      { type: "align", value: "right", icon: AlignRight, label: "Align Right" },
      { type: "align", value: "justify", icon: AlignJustify, label: "Justify" },
    ]
  },
  {
    name: "colors",
    items: [
      { type: "color", icon: Palette, label: "Text Color" },
      { type: "background", icon: Palette, label: "Background Color" },
    ]
  },
  {
    name: "media",
    items: [
      { type: "link", icon: Link, label: "Insert Link" },
      { type: "image", icon: Image, label: "Insert Image" },
      { type: "video", icon: Video, label: "Insert Video" },
    ]
  }
];

interface ToolbarItem {
  type: string;
  value?: string | number;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

interface ActiveFormats {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  strike?: boolean;
  header?: number;
  blockquote?: boolean;
  'code-block'?: boolean;
  list?: string;
  align?: string;
  color?: string;
  background?: string;
  link?: string;
}

interface HeadlessToolbarProps {
  quillRef: React.RefObject<ReactQuillComponent | null>;
  isVisible: boolean;
  onClose: () => void;
  position: { top: number; left: number };
  activeFormats?: ActiveFormats;
}

interface ColorPickerProps {
  onColorSelect: (color: string) => void;
  onClose: () => void;
  type: 'text' | 'background';
}

function ColorPicker({ onColorSelect, onClose, type }: ColorPickerProps) {
  return (
    <Card className="absolute top-full left-0 mt-1 p-2 shadow-lg border bg-background z-50 min-w-[200px]">
      <div className="mb-2">
        <p className="text-xs font-medium text-muted-foreground">
          {type === 'text' ? 'Text Color' : 'Background Color'}
        </p>
      </div>
      <div className="grid grid-cols-7 gap-1 mb-2">
        {colors.map((color, index) => (
          <button
            key={index}
            type="button"
            className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
            style={{ backgroundColor: color }}
            onClick={() => {
              onColorSelect(color);
              onClose();
            }}
            title={color}
          />
        ))}
      </div>
      <div className="flex items-center gap-2 pt-2 border-t">
        <input
          type="color"
          className="w-8 h-6 rounded border cursor-pointer"
          onChange={(e) => {
            onColorSelect(e.target.value);
            onClose();
          }}
          title="Custom color"
        />
        <span className="text-xs text-muted-foreground">Custom</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="ml-auto h-6 w-6 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </Card>
  );
}

function HeadlessToolbar({ quillRef, isVisible, onClose, position, activeFormats = {} }: HeadlessToolbarProps) {
  const [mounted, setMounted] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState<'text' | 'background' | null>(null);
  const [showLinkPopover, setShowLinkPopover] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [showImagePopover, setShowImagePopover] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [showVideoPopover, setShowVideoPopover] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');

  // Helper function to check if a format is active
  const isFormatActive = (item: ToolbarItem): boolean => {
    switch (item.type) {
      case 'bold':
      case 'italic':
      case 'underline':
      case 'strike':
      case 'blockquote':
      case 'code-block':
        return Boolean(activeFormats[item.type as keyof ActiveFormats]);
      case 'header':
        return activeFormats.header === item.value;
      case 'list':
        return activeFormats.list === item.value;
      case 'align':
        return activeFormats.align === item.value;
      default:
        return false;
    }
  };

  // Get button style with active state support
  const getButtonStyle = (isActive: boolean = false) => ({
    border: 'none',
    padding: '6px',
    borderRadius: '4px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '32px',
    height: '32px',
    flexShrink: 0,
    transition: 'all 0.2s ease',
    backgroundColor: isActive ? 'hsl(var(--primary))' : 'transparent',
    color: isActive ? 'hsl(var(--primary-foreground))' : 'inherit',
    borderColor: isActive ? 'hsl(var(--primary))' : 'transparent',
  });

  // Get button class name based on active state
  const getButtonClassName = (isActive: boolean) => {
    return isActive ? 'headless-toolbar-button-active' : '';
  };

  useEffect(() => {
    console.log('🔧 HeadlessToolbar mounting...');
    setMounted(true);
    return () => {
      console.log('🔧 HeadlessToolbar unmounting...');
    };
  }, []);

  const handleColorSelect = useCallback((color: string, type: 'text' | 'background') => {
    if (!quillRef.current) return;

    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();

    if (!range) return;

    if (type === 'text') {
      quill.format('color', color);
    } else {
      quill.format('background', color);
    }

    quill.focus();
  }, [quillRef]);

  const handleLinkInsert = useCallback(() => {
    if (!quillRef.current || !linkUrl.trim()) return;

    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();

    if (!range) return;

    console.log(`🔗 Applying link:`, linkUrl);
    quill.format("link", linkUrl);

    // Reset and close popover
    setLinkUrl('');
    setShowLinkPopover(false);

    // Maintain selection after formatting
    setTimeout(() => {
      quill.setSelection(range);
      quill.focus();
    }, 10);
  }, [quillRef, linkUrl]);

  const handleImageInsert = useCallback(() => {
    if (!quillRef.current || !imageUrl.trim()) return;

    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();

    if (!range) return;

    console.log(`🖼️ Inserting image:`, imageUrl);
    quill.insertEmbed(range.index, "image", imageUrl);

    // Reset and close popover
    setImageUrl('');
    setShowImagePopover(false);

    // Focus editor
    setTimeout(() => {
      quill.focus();
    }, 10);
  }, [quillRef, imageUrl]);

  const handleVideoInsert = useCallback(() => {
    if (!quillRef.current || !videoUrl.trim()) return;

    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();

    if (!range) return;

    console.log(`🎥 Inserting video:`, videoUrl);
    quill.insertEmbed(range.index, "video", videoUrl);

    // Reset and close popover
    setVideoUrl('');
    setShowVideoPopover(false);

    // Focus editor
    setTimeout(() => {
      quill.focus();
    }, 10);
  }, [quillRef, videoUrl]);

  const handleToolbarAction = useCallback((item: ToolbarItem) => {
    console.log('🔧 Toolbar action triggered:', item);

    if (!quillRef.current) {
      console.error('❌ No quill ref available');
      return;
    }

    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();

    

    if (!range) {
      console.error('❌ No selection range available');
      return;
    }

    const currentFormat = quill.getFormat(range);
    console.log('🎨 Current format:', currentFormat);

    switch (item.type) {
      case "bold":
      case "italic":
      case "underline":
      case "strike":
        const newValue = !currentFormat[item.type];
        console.log(`🎨 Applying ${item.type}:`, newValue);
        quill.format(item.type, newValue);
        break;
      case "blockquote":
      case "code-block":
        const blockValue = !currentFormat[item.type];
        console.log(`📝 Applying ${item.type}:`, blockValue);
        quill.format(item.type, blockValue);
        break;
      case "header":
        const currentHeader = currentFormat.header;
        const headerValue = currentHeader === item.value ? false : item.value;
        console.log(`📰 Applying header:`, headerValue);
        quill.format("header", headerValue);
        break;
      case "list":
        const currentList = currentFormat.list;
        const listValue = currentList === item.value ? false : item.value;
        console.log(`📋 Applying list:`, listValue);
        quill.format("list", listValue);
        break;
      case "align":
        const alignValue = item.value || false;
        console.log(`📐 Applying align:`, alignValue);
        quill.format("align", alignValue);
        break;
      case "color":
        setShowColorPicker('text');
        return; // Don't close toolbar, let color picker handle it
      case "background":
        setShowColorPicker('background');
        return; // Don't close toolbar, let color picker handle it
      case "link":
        setShowLinkPopover(true);
        return; // Don't close toolbar, let link popover handle it
      case "image":
        setShowImagePopover(true);
        return; // Don't close toolbar, let image popover handle it
      case "video":
        setShowVideoPopover(true);
        return; // Don't close toolbar, let video popover handle it
      default:
        console.error(`❓ Unknown action type:`, item.type);
    }

    // Maintain selection after formatting
    setTimeout(() => {
      quill.setSelection(range);
      quill.focus();
    }, 10);

   
  }, [quillRef]);

 

  if (!isVisible || !mounted) {
    
    return null;
  }

  

  // Enhanced toolbar with better stacking context handling
  const toolbarContent = (
    <div
      className="headless-toolbar-enter !flex !flex-row !items-center !justify-center !gap-1"
      data-headless-toolbar="true"
      style={{
        position: 'fixed',
        top: `${position.top}px`,
        left: `${position.left}px`,
        transform: 'translate(-50%, 0%)', // Center horizontally, no vertical offset
        zIndex: 2147483647, // Maximum z-index value
        pointerEvents: 'auto',
        background: 'white',
        border: '1px solid #e2e8f0',
        borderRadius: '8px',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
        padding: '8px',
        display: 'flex',
        flexDirection: 'row', // Ensure horizontal layout
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        maxWidth: '90vw',
        minWidth: 'max-content', // Prevent wrapping
        overflowX: 'auto',
        isolation: 'isolate', // Create new stacking context
        willChange: 'transform', // Optimize for animations
        whiteSpace: 'nowrap', // Prevent text wrapping
      }}
    >

      {/* All toolbar items in a single horizontal row */}
      <div className="!flex !flex-row !items-center !justify-center !gap-1 !flex-nowrap" style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '4px', flexWrap: 'nowrap' }}>
        {headlessToolbarGroups.map((group, groupIndex) => (
          <React.Fragment key={groupIndex}>
            {group.items.map((item, itemIndex) => {
            const IconComponent = item.icon;
            const isColorButton = item.type === 'color' || item.type === 'background';
            const isMediaButton = item.type === 'link' || item.type === 'image' || item.type === 'video';
            const isActive = isFormatActive(item);

            if (isColorButton) {
              return (
                <div key={itemIndex} style={{ position: 'relative' }}>
                  <button
                    type="button"
                    onClick={() => handleToolbarAction(item)}
                    style={getButtonStyle(isActive)}
                    className={getButtonClassName(isActive)}
                    onMouseEnter={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                      }
                    }}
                    title={item.label}
                  >
                    <IconComponent style={{ width: '16px', height: '16px' }} />
                  </button>
                  {showColorPicker === item.type && (
                    <ColorPicker
                      type={item.type as 'text' | 'background'}
                      onColorSelect={(color) => handleColorSelect(color, item.type as 'text' | 'background')}
                      onClose={() => setShowColorPicker(null)}
                    />
                  )}
                </div>
              );
            }

            if (isMediaButton) {
              return (
                <div key={itemIndex} style={{ position: 'relative' }}>
                  <Popover
                    open={
                      (item.type === 'link' && showLinkPopover) ||
                      (item.type === 'image' && showImagePopover) ||
                      (item.type === 'video' && showVideoPopover)
                    }
                    onOpenChange={(open) => {
                      if (item.type === 'link') setShowLinkPopover(open);
                      else if (item.type === 'image') setShowImagePopover(open);
                      else if (item.type === 'video') setShowVideoPopover(open);
                    }}
                  >
                    <PopoverTrigger asChild>
                      <button
                        type="button"
                        onClick={() => handleToolbarAction(item)}
                        style={getButtonStyle(isActive)}
                        className={getButtonClassName(isActive)}
                        onMouseEnter={(e) => {
                          if (!isActive) {
                            e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!isActive) {
                            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                          }
                        }}
                        title={item.label}
                      >
                        <IconComponent style={{ width: '16px', height: '16px' }} />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-4" align="start">
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor={`${item.type}-url`} className="text-sm font-medium">
                            {item.type === 'link' ? 'Link URL' :
                             item.type === 'image' ? 'Image URL' : 'Video URL'}
                          </Label>
                          <Input
                            id={`${item.type}-url`}
                            type="url"
                            placeholder={
                              item.type === 'link' ? 'https://example.com' :
                              item.type === 'image' ? 'https://example.com/image.jpg' :
                              'https://example.com/video.mp4'
                            }
                            value={
                              item.type === 'link' ? linkUrl :
                              item.type === 'image' ? imageUrl : videoUrl
                            }
                            onChange={(e) => {
                              if (item.type === 'link') setLinkUrl(e.target.value);
                              else if (item.type === 'image') setImageUrl(e.target.value);
                              else if (item.type === 'video') setVideoUrl(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                if (item.type === 'link') handleLinkInsert();
                                else if (item.type === 'image') handleImageInsert();
                                else if (item.type === 'video') handleVideoInsert();
                              }
                            }}
                            className="mt-1"
                          />
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (item.type === 'link') {
                                setLinkUrl('');
                                setShowLinkPopover(false);
                              } else if (item.type === 'image') {
                                setImageUrl('');
                                setShowImagePopover(false);
                              } else if (item.type === 'video') {
                                setVideoUrl('');
                                setShowVideoPopover(false);
                              }
                            }}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => {
                              if (item.type === 'link') handleLinkInsert();
                              else if (item.type === 'image') handleImageInsert();
                              else if (item.type === 'video') handleVideoInsert();
                            }}
                            disabled={
                              (item.type === 'link' && !linkUrl.trim()) ||
                              (item.type === 'image' && !imageUrl.trim()) ||
                              (item.type === 'video' && !videoUrl.trim())
                            }
                          >
                            {item.type === 'link' ? 'Add Link' :
                             item.type === 'image' ? 'Insert Image' : 'Insert Video'}
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              );
            }

            return (
              <button
                key={itemIndex}
                type="button"
                onClick={() => handleToolbarAction(item)}
                style={getButtonStyle(isActive)}
                className={getButtonClassName(isActive)}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                  }
                }}
                title={item.label}
              >
                <IconComponent style={{
                  width: '16px',
                  height: '16px',
                  color: isActive ? 'hsl(var(--primary-foreground))' : 'inherit'
                }} />
              </button>
            );
          })}
          {/* Add separator between groups (except after the last group) */}
          {groupIndex < headlessToolbarGroups.length - 1 && (
            <div style={{
              width: '1px',
              height: '24px',
              background: '#e2e8f0',
              margin: '0 4px',
              flexShrink: 0
            }} />
          )}
        </React.Fragment>
      ))}

      {/* Separator before close button */}
      <div style={{
        width: '1px',
        height: '24px',
        background: '#e2e8f0',
        margin: '0 4px',
        flexShrink: 0
      }} />

        {/* Close button */}
        <button
          type="button"
          onClick={onClose}
          style={{
            background: 'transparent',
            border: 'none',
            padding: '6px',
            borderRadius: '4px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '32px',
            height: '32px',
            flexShrink: 0,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#fee2e2';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
          }}
          title="Close toolbar"
        >
          <X style={{ width: '16px', height: '16px' }} />
        </button>
      </div>
    </div>
  );



  // Use createPortal to render the toolbar at the document body level to bypass stacking context issues
  return createPortal(toolbarContent, document.body);
}

export interface HybridQuillEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  readOnly?: boolean;
  showHeadlessToolbar?: boolean;
}

export default function HybridQuillEditor({
  value,
  onChange,
  className,
  placeholder,
  readOnly,
  showHeadlessToolbar = true
}: HybridQuillEditorProps) {
  const quillRef = useRef<ReactQuillComponent | null>(null);
  const [toolbarVisible, setToolbarVisible] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ top: 0, left: 0 });
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [activeFormats, setActiveFormats] = useState<ActiveFormats>({});



  const modules = useMemo(() => ({
    toolbar: { 
      container: "#quill-toolbar"
    }
  }), []);

  const handleSelectionChange = useCallback((range: { index: number; length: number } | null) => {
   



    if (!quillRef.current || !showHeadlessToolbar || !isEditorReady) {
      
      setToolbarVisible(false);
      return;
    }

    // If no range or empty selection, hide toolbar
    if (!range || range.length === 0) {
      
      setToolbarVisible(false);
      return;
    }

    // Show toolbar for any selection with length > 0
    if (range.length > 0) {
      
      const quill = quillRef.current.getEditor();

      try {
        // Get current formatting of the selection
        const currentFormat = quill.getFormat(range);
       

        // Update active formats state with proper type checking
        setActiveFormats({
          bold: Boolean(currentFormat.bold),
          italic: Boolean(currentFormat.italic),
          underline: Boolean(currentFormat.underline),
          strike: Boolean(currentFormat.strike),
          header: typeof currentFormat.header === 'number' ? currentFormat.header : undefined,
          blockquote: Boolean(currentFormat.blockquote),
          'code-block': Boolean(currentFormat['code-block']),
          list: typeof currentFormat.list === 'string' ? currentFormat.list : undefined,
          align: typeof currentFormat.align === 'string' ? currentFormat.align : undefined,
          color: typeof currentFormat.color === 'string' ? currentFormat.color : undefined,
          background: typeof currentFormat.background === 'string' ? currentFormat.background : undefined,
          link: typeof currentFormat.link === 'string' ? currentFormat.link : undefined,
        });

        // Get the bounds of the selection
        const bounds = quill.getBounds(range.index, range.length);
        const editorRect = quill.container.getBoundingClientRect();

      

        // Fixed positioning calculation - use viewport-relative coordinates
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const toolbarWidth = 400; // Approximate toolbar width
        const toolbarHeight = 50; // Approximate toolbar height

       

        // Calculate position relative to viewport (not document)
        // editorRect is already viewport-relative, bounds is relative to editor
        let left = editorRect.left + bounds.left + (bounds.width / 2);
        let top = editorRect.top + bounds.top;

       

        // Ensure toolbar stays within viewport horizontally
        const minLeft = 16;
        const maxLeft = viewportWidth - toolbarWidth - 16;

        if (left - toolbarWidth / 2 < minLeft) {
          left = minLeft + toolbarWidth / 2;
        } else if (left + toolbarWidth / 2 > maxLeft) {
          left = maxLeft - toolbarWidth / 2;
        }

        // Ensure toolbar stays within viewport vertically
        const minTop = 16;
        const maxTop = viewportHeight - toolbarHeight - 16;

        if (top - toolbarHeight - 8 < minTop) {
          // Position below selection if not enough space above
          top = editorRect.top + bounds.top + bounds.height + 8;
          if (top + toolbarHeight > maxTop) {
            top = maxTop - toolbarHeight;
          }
        } else {
          // Position above selection (default)
          top = top - toolbarHeight - 8;
        }

       
        setToolbarPosition({ top, left });
        setToolbarVisible(true);
        
      } catch (error) {
        console.error('❌ Error calculating toolbar position:', error);

        setToolbarVisible(false);
      }
    }
  }, [showHeadlessToolbar, isEditorReady]);

  useEffect(() => {
    // Wait for Quill to be fully initialized
    const initializeEventListeners = () => {
      if (!quillRef.current) {
        
        setTimeout(initializeEventListeners, 100);
        return;
      }

      const quill = quillRef.current.getEditor();
      if (!quill) {
        
        setTimeout(initializeEventListeners, 100);
        return;
      }

      const editorElement = quill.container;
      

      // Mark editor as ready
      setIsEditorReady(true);

      // Listen for selection changes
      quill.on('selection-change', handleSelectionChange);

      // Enhanced mouse events to handle form interference
      const handleMouseUp = (e: MouseEvent) => {
        
        // Prevent form elements from interfering
        e.stopPropagation();
        setTimeout(() => {
          const selection = quill.getSelection();
          
          if (selection && selection.length > 0) {
            handleSelectionChange(selection);
          }
        }, 100); // Increased delay for better reliability
      };

      const handleMouseDown = (e: MouseEvent) => {
        
        // Don't hide toolbar immediately, let selection complete first
        e.stopPropagation();
      };

      const handleKeyUp = (e: KeyboardEvent) => {
        
        // Prevent form submission interference
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
          e.preventDefault();
          e.stopPropagation();
        }

        // Check selection for various key combinations
        if (e.key.includes('Arrow') || e.shiftKey || e.ctrlKey || e.metaKey || e.key === 'Escape') {
          setTimeout(() => {
            const selection = quill.getSelection();
            
            if (selection && selection.length > 0) {
              handleSelectionChange(selection);
            } else if (selection && selection.length === 0) {
              setToolbarVisible(false);
            }
          }, 100);
        }
      };

      // Handle text selection with double-click
      const handleDoubleClick = (e: MouseEvent) => {
        
        e.stopPropagation();
        setTimeout(() => {
          const selection = quill.getSelection();
          
          if (selection && selection.length > 0) {
            handleSelectionChange(selection);
          }
        }, 100);
      };

      // Handle focus events to ensure proper initialization
      const handleFocus = () => {
        setTimeout(() => {
          const selection = quill.getSelection();
          if (selection && selection.length > 0) {
            handleSelectionChange(selection);
          }
        }, 100);
      };

      // Handle blur to hide toolbar when editor loses focus
      const handleBlur = (e: FocusEvent) => {
        // Only hide if focus is not moving to toolbar
        const relatedTarget = e.relatedTarget as HTMLElement;
        if (!relatedTarget || !relatedTarget.closest('[data-headless-toolbar]')) {
          setTimeout(() => setToolbarVisible(false), 200);
        }
      };

      editorElement.addEventListener('mouseup', handleMouseUp);
      editorElement.addEventListener('mousedown', handleMouseDown);
      editorElement.addEventListener('keyup', handleKeyUp);
      editorElement.addEventListener('dblclick', handleDoubleClick);
      editorElement.addEventListener('focus', handleFocus);
      editorElement.addEventListener('blur', handleBlur);

      // Cleanup function
      return () => {
                quill.off('selection-change', handleSelectionChange);
        editorElement.removeEventListener('mouseup', handleMouseUp);
        editorElement.removeEventListener('mousedown', handleMouseDown);
        editorElement.removeEventListener('keyup', handleKeyUp);
        editorElement.removeEventListener('dblclick', handleDoubleClick);
        editorElement.removeEventListener('focus', handleFocus);
        editorElement.removeEventListener('blur', handleBlur);
      };
    };

    const cleanup = initializeEventListeners();
    return cleanup;
  }, [handleSelectionChange]);

  return (
    <div className={cn("relative", className)}>
      
      <div className="border rounded-md overflow-hidden hybrid-quill">
        <QuillCustomToolbar />
        <ReactQuill
          ref={quillRef}
          value={value}
          onChange={onChange}
          modules={modules}
          theme="snow"
          placeholder={placeholder}
          readOnly={readOnly}
        />
      </div>

      {showHeadlessToolbar && typeof window !== 'undefined' && (
        <>
          <HeadlessToolbar
            quillRef={quillRef}
            isVisible={toolbarVisible}
            onClose={() => setToolbarVisible(false)}
            position={toolbarPosition}
            activeFormats={activeFormats}
          />
          {/* Debug element to test if portal rendering works */}
          {process.env.NODE_ENV === 'development' && toolbarVisible && createPortal(
            <div
              style={{
                position: 'fixed',
                top: '10px',
                right: '10px',
                background: 'red',
                color: 'white',
                padding: '10px',
                zIndex: 999999,
                border: '2px solid yellow'
              }}
            >
              TOOLBAR ACTIVE - Portal Working
            </div>,
            document.body
          )}

          {/* Fallback toolbar for debugging - shows if main toolbar fails */}
          {process.env.NODE_ENV === 'development' && toolbarVisible && (
            <div
              style={{
                position: 'absolute',
                top: '100%',
                left: '50%',
                transform: 'translateX(-50%)',
                background: 'orange',
                color: 'black',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '12px',
                zIndex: 1000,
                marginTop: '8px'
              }}
            >
              Fallback Toolbar (if main toolbar not visible)
            </div>
          )}
        </>
      )}
    </div>
  );
}
