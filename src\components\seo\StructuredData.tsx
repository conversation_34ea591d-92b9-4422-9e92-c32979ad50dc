'use client';

import { generateStructuredData } from '@/lib/metadata-utils';

interface StructuredDataProps {
  type: 'organization' | 'localBusiness' | 'service' | 'educationalOrganization';
  data?: {
    name?: string;
    description?: string;
    serviceType?: string[] | string;
  };
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const structuredData = generateStructuredData(type, data);

  if (!structuredData) {
    return null;
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2),
      }}
    />
  );
}

// Specific structured data components for different page types
export function OrganizationStructuredData() {
  return <StructuredData type="organization" />;
}

export function EducationalOrganizationStructuredData() {
  return <StructuredData type="educationalOrganization" />;
}

export function ServiceStructuredData({ 
  name, 
  description, 
  serviceType 
}: { 
  name?: string; 
  description?: string; 
  serviceType?: string[] | string; 
}) {
  return (
    <StructuredData 
      type="service" 
      data={{ name, description, serviceType }} 
    />
  );
}

// Combined structured data for homepage
export function HomepageStructuredData() {
  return (
    <>
      <OrganizationStructuredData />
      <EducationalOrganizationStructuredData />
      <ServiceStructuredData 
        name="Academic Writing Services"
        description="Professional academic writing assistance for students at all levels"
        serviceType={[
          "Essay Writing",
          "Research Papers", 
          "Dissertation Writing",
          "Term Papers",
          "Literature Reviews"
        ]}
      />
    </>
  );
}
