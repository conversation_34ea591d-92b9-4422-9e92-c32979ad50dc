@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.606 0.25 292.717);
  --primary-foreground: oklch(0.969 0.016 293.756);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.606 0.25 292.717);
  --chart-1: oklch(0.646 0.222 41.116); /* purple/violet */
  --chart-2: oklch(0.6 0.118 184.704); /* Teal/Cyan */
  --chart-3: oklch(0.398 0.07 227.392); /* Yellow/Gold */
  --chart-4: oklch(0.828 0.189 84.429); /* Magenta/Pink */
  --chart-5: oklch(0.769 0.188 70.08); /* Red/Orange */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.606 0.25 292.717);
  --sidebar-primary-foreground: oklch(0.969 0.016 293.756);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.606 0.25 292.717);
}

.dark {
  /* Modern Dark Blue Theme - Similar to Discord/VS Code */
  --background: oklch(0.15 0.02 240); /* Deep navy blue background */
  --foreground: oklch(0.95 0.005 240); /* Light blue-white text */
  --card: oklch(0.18 0.025 240); /* Slightly lighter navy for cards */
  --card-foreground: oklch(0.95 0.005 240);
  --popover: oklch(0.18 0.025 240);
  --popover-foreground: oklch(0.95 0.005 240);
  --primary: oklch(0.541 0.281 293.009); /* Keep existing primary color */
  --primary-foreground: oklch(0.969 0.016 293.756);
  --secondary: oklch(0.22 0.03 240); /* Medium dark blue for secondary */
  --secondary-foreground: oklch(0.95 0.005 240);
  --muted: oklch(0.22 0.03 240); /* Medium dark blue for muted elements */
  --muted-foreground: oklch(0.7 0.02 240); /* Muted blue-gray text */
  --accent: oklch(0.25 0.035 240); /* Accent blue */
  --accent-foreground: oklch(0.95 0.005 240);
  --destructive: oklch(0.704 0.191 22.216); /* Keep red for destructive */
  --border: oklch(0.25 0.035 240 / 40%); /* Blue-tinted borders */
  --input: oklch(0.2 0.03 240); /* Dark blue inputs */
  --ring: oklch(0.541 0.281 293.009); /* Keep primary ring color */
  --chart-1: oklch(0.488 0.243 264.376); /* Orange */
  --chart-2: oklch(0.696 0.17 162.48); /* Teal/turquoise */
  --chart-3: oklch(0.769 0.188 70.08); /* Dark blue */
  --chart-4: oklch(0.627 0.265 303.9); /* Yellow/lime */
  --chart-5: oklch(0.645 0.246 16.439); /* Yellow/gold */
  --sidebar: oklch(0.16 0.025 240); /* Sidebar dark blue */
  --sidebar-foreground: oklch(0.95 0.005 240);
  --sidebar-primary: oklch(0.541 0.281 293.009);
  --sidebar-primary-foreground: oklch(0.969 0.016 293.756);
  --sidebar-accent: oklch(0.22 0.03 240); /* Sidebar accent blue */
  --sidebar-accent-foreground: oklch(0.95 0.005 240);
  --sidebar-border: oklch(0.25 0.035 240 / 30%); /* Blue-tinted sidebar borders */
  --sidebar-ring: oklch(0.541 0.281 293.009);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom blog content styles to ensure headings are properly sized */
@layer components {
  .blog-content h1,
  .blog-content h2,
  .blog-content h3,
  .blog-content h4,
  .blog-content h5,
  .blog-content h6 {
    scroll-margin-top: 120px; /* Fixed header offset */
    scroll-behavior: smooth;
  }

  .blog-content h1 {
    @apply text-4xl font-bold mt-8 mb-6 text-foreground;
  }

  .blog-content h2 {
    @apply text-3xl font-semibold mt-12 mb-4 text-foreground;
  }

  .blog-content h3 {
    @apply text-2xl font-semibold mt-8 mb-3 text-foreground;
  }

  .blog-content h4 {
    @apply text-xl font-semibold mt-6 mb-3 text-foreground;
  }

  .blog-content h5 {
    @apply text-lg font-semibold mt-4 mb-2 text-foreground;
  }

  .blog-content h6 {
    @apply text-base font-semibold mt-4 mb-2 text-foreground;
  }

  .blog-content p {
    @apply text-base leading-relaxed mb-4 text-foreground;
  }

  .blog-content strong {
    @apply font-semibold text-foreground;
  }

  .blog-content a {
    @apply text-primary no-underline hover:underline;
  }

  .blog-content blockquote {
    @apply border-l-4 border-primary pl-6 italic text-muted-foreground;
  }

  .blog-content code {
    @apply bg-muted px-2 py-1 rounded-md text-sm;
  }

  .blog-content pre {
    @apply bg-muted p-4 rounded-lg overflow-x-auto;
  }

  .blog-content ul {
    @apply list-disc pl-6;
  }

  .blog-content ol {
    @apply list-decimal pl-6;
  }

  .blog-content li {
    @apply mb-2;
  }

  .blog-content img {
    @apply rounded-lg shadow-md;
  }
}

@layer components {
  .footer-bg {
    background-color: oklch(0.16 0.025 240); /* Dark blue footer background */
  }

  .disclaimer-bg {
    background-color: oklch(0.18 0.03 240); /* Slightly lighter dark blue */
  }

  .footer-text {
    color: oklch(0.95 0.005 240) !important; /* Light blue-white text */
  }

  .footer-text-muted {
    color: oklch(0.7 0.02 240) !important; /* Muted blue-gray text */
  }

  .footer-link {
    color: oklch(0.95 0.005 240) !important; /* Light blue-white for links */
    &:hover {
      color: oklch(0.541 0.281 293.009) !important; /* Primary color for hover */
    }
  }
}

/* Testimonials page animations */
@layer components {
  .animate-scroll-right-to-left {
    animation: scroll-right-to-left 60s linear infinite;
  }

  .animate-scroll-left-to-right {
    animation: scroll-left-to-right 60s linear infinite;
  }

  .animate-spin-slow {
    animation: spin 20s linear infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@keyframes scroll-right-to-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-left-to-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Headless toolbar styles - ensure maximum visibility */
@layer components {
  .headless-toolbar-enter {
    z-index: 999999 !important;
    pointer-events: auto !important;
    position: fixed !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .headless-toolbar-enter * {
    pointer-events: auto !important;
  }

  /* Active toolbar button styling with maximum specificity */
  .headless-toolbar-enter .headless-toolbar-button-active,
  .headless-toolbar-enter button.headless-toolbar-button-active,
  [data-headless-toolbar="true"] .headless-toolbar-button-active,
  [data-headless-toolbar="true"] button.headless-toolbar-button-active,
  button.headless-toolbar-button-active {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    border-color: hsl(var(--primary)) !important;
    box-shadow: 0 0 0 1px hsl(var(--primary)) !important;
  }

  .headless-toolbar-enter .headless-toolbar-button-active:hover,
  .headless-toolbar-enter button.headless-toolbar-button-active:hover,
  [data-headless-toolbar="true"] .headless-toolbar-button-active:hover,
  [data-headless-toolbar="true"] button.headless-toolbar-button-active:hover,
  button.headless-toolbar-button-active:hover {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    opacity: 0.9 !important;
    border-color: hsl(var(--primary)) !important;
    box-shadow: 0 0 0 1px hsl(var(--primary)) !important;
  }

  /* Ensure active state overrides all shadcn/ui button variants */
  .headless-toolbar-enter .headless-toolbar-button-active.bg-accent,
  .headless-toolbar-enter .headless-toolbar-button-active.hover\:bg-accent:hover {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
  }

  /* Enhanced drawer styles */
  .drawer-content {
    transition: all 0.3s cubic-bezier(0.32, 0.72, 0, 1);
  }

  .drawer-overlay {
    backdrop-filter: blur(4px);
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
  }

  /* Custom scrollbar for drawer content */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.3);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.5);
  }

  /* Dark mode scrollbar */
  .dark .scrollbar-thin {
    scrollbar-color: rgba(75, 85, 99, 0.4) transparent;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.4);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(75, 85, 99, 0.6);
  }

  /* Smooth animations for drawer cards */
  .drawer-card-enter {
    animation: slideInUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced gradient backgrounds */
  .gradient-card {
    background: linear-gradient(135deg,
      rgba(var(--primary), 0.05) 0%,
      transparent 50%,
      rgba(var(--primary), 0.02) 100%);
  }
}